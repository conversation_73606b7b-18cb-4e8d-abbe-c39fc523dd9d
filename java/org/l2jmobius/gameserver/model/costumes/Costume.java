/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.model.costumes;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.skill.Skill;

/**
 * <AUTHOR>
 */
public class Costume
{
	private int		_playerId;
	private int		_id;
	private long	_amount;
	private boolean	_locked;
	private boolean	_isNew;
	private Skill	skill;
	
	public static Costume of(int costumeId, Player player)
	{
		final Costume data = new Costume();
		data._playerId = player.getObjectId();
		data._id = costumeId;
		data._isNew = true;
		return data;
	}
	
	public Skill getSkill()
	{
		return skill;
	}
	
	public void increaseAmount()
	{
		_amount++;
		update();
	}
	
	public void setLockedAndUpdate(boolean lock)
	{
		_locked = lock;
		update();
	}
	
	public void setLocked(boolean lock)
	{
		_locked = lock;
	}
	
	public boolean isLocked()
	{
		return _locked;
	}
	
	public void reduceCount(long amount)
	{
		_amount -= amount;
		update();
	}
	
	public int getPlayerId()
	{
		return _playerId;
	}
	
	public void setPlayerId(int playerId)
	{
		_playerId = playerId;
	}
	
	public int getId()
	{
		return _id;
	}
	
	public void setId(int id)
	{
		_id = id;
	}
	
	public long getAmount()
	{
		return _amount;
	}
	
	public void setAmount(long amount)
	{
		_amount = amount;
	}
	
	public void setNew(boolean isNew)
	{
		_isNew = isNew;
	}
	
	public boolean checkIsNewAndChange()
	{
		final boolean ret = _isNew;
		_isNew = false;
		return ret;
	}
	
	private void update()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE character_costumes SET amount = ?, locked = ? WHERE player_id = ? AND id = ?"))
		{
			ps.setLong(1, _amount);
			ps.setBoolean(2, _locked);
			ps.setInt(3, _playerId);
			ps.setInt(4, _id);
			int updated = ps.executeUpdate();

			java.util.logging.Logger.getLogger(Costume.class.getName()).info("Updated costume " + _id + " for player " + _playerId + " - Amount: " + _amount + ", Locked: " + _locked + " (Rows affected: " + updated + ")");
		}
		catch (SQLException e)
		{
			java.util.logging.Logger.getLogger(Costume.class.getName()).warning("Failed to update costume " + _id + " for player " + _playerId + ": " + e.getMessage());
			e.printStackTrace();
		}
	}
	
	public void load()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT amount, locked FROM character_costumes WHERE player_id = ? AND id = ?"))
		{
			ps.setInt(1, _playerId);
			ps.setInt(2, _id);
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next())
				{
					_amount = rs.getLong("amount");
					_locked = rs.getBoolean("locked");
				}
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
	}
	
	public static List<Costume> getCostumesForPlayer(Player player)
	{
		final List<Costume> costumes = new ArrayList<>();
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT id, amount, locked FROM character_costumes WHERE player_id = ?"))
		{
			ps.setInt(1, player.getObjectId());
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					final Costume data = new Costume();
					data._playerId = player.getObjectId();
					data._id = rs.getInt("id");
					data._amount = rs.getLong("amount");
					data._locked = rs.getBoolean("locked");
					data._isNew = false; // Assume that costumes loaded from DB are not new.
					costumes.add(data);
				}
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
		return costumes;
	}
}