/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.model.costumes;

/**
 * <AUTHOR>
 */
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.gameserver.model.actor.Player;

public class CostumeCollectionData
{
	private static final String			UPDATE_REUSE_TIME	= "UPDATE character_costume_collection SET reuse=? WHERE player_id=? AND id=?";
	private int							_playerId;
	private int							_id;
	private int							_reuse;
	public static CostumeCollectionData	DEFAULT				= new CostumeCollectionData();
	
	public static CostumeCollectionData of(Player player, int id, int reuseTime)
	{
		final CostumeCollectionData collection = new CostumeCollectionData();
		collection._playerId = player.getObjectId();
		collection._id = id;
		collection._reuse = reuseTime;
		return collection;
	}
	
	public int getId()
	{
		return _id;
	}
	
	public void updateReuseTime()
	{
		_reuse = (int) Instant.now().plus(Duration.ofMinutes(10)).getEpochSecond();
		updateReuseTimeInDB();
	}
	
	private void updateReuseTimeInDB()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement(UPDATE_REUSE_TIME))
		{
			ps.setInt(1, _reuse);
			ps.setInt(2, _playerId);
			ps.setInt(3, _id);
			ps.executeUpdate();
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
	}
	
	public int getReuseTime()
	{
		return (int) Math.max(0, _reuse - Instant.now().getEpochSecond());
	}
	
	public static void createCostumeInDB(Player player, int id, int reuseTime)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO character_costume_collection (player_id, id, reuse) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE reuse=VALUES(reuse)"))
		{
			ps.setInt(1, player.getObjectId());
			ps.setInt(2, id);
			ps.setInt(3, reuseTime);
			ps.executeUpdate();
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
	}
}