/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.model.costumes;

import org.l2jmobius.gameserver.model.StatSet;

/**
 * <AUTHOR>
 */
public class CostumeDataHolder
{
	private final int		_id;
	private final StatSet	_attributes;
	
	/**
	 * Constructs a new CostumeDataHolder.
	 * 
	 * @param statSet
	 *            the StatSet containing attributes of the costume
	 */
	public CostumeDataHolder(StatSet statSet)
	{
		_id = statSet.getInt("id");
		_attributes = statSet;
	}
	
	/**
	 * Gets the ID of the costume.
	 * 
	 * @return the ID of the costume
	 */
	public int getId()
	{
		return _id;
	}
	
	/**
	 * Gets the value of a specific attribute for the costume.
	 * 
	 * @param attributeName
	 *            the name of the attribute
	 * @return the value of the attribute, or null if the attribute does not exist
	 */
	public String getAttribute(String attributeName)
	{
		return _attributes.getString(attributeName);
	}
}