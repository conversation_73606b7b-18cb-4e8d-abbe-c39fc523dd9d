/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.clientpackets.costume;

import java.util.List;

import org.l2jmobius.gameserver.enums.ItemSkillType;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.costumes.CostumeManager;
import org.l2jmobius.gameserver.model.holders.ItemSkillHolder;
import org.l2jmobius.gameserver.model.item.instance.Item;
import org.l2jmobius.gameserver.model.skill.SkillCaster;
import org.l2jmobius.gameserver.network.clientpackets.ClientPacket;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExSendCostumeListFull;

/**
 * <AUTHOR>
 */
public class ExRequestCostumeUseItem extends ClientPacket
{
	private int _itemObjectId;
	
	@Override
	protected void readImpl()
	{
		_itemObjectId = readInt();
	}
	
	@Override
	protected void runImpl()
	{
		final Player player = getPlayer();
		if (player == null)
		{
			return;
		}

		final Item item = player.getInventory().getItemByObjectId(_itemObjectId);
		if (item == null)
		{
			return;
		}

		// Check if player can perform costume actions
		if (!CostumeManager.getInstance().checkCostumeAction(player))
		{
			return;
		}

		// Use the costume item
		final List<ItemSkillHolder> skills = item.getTemplate().getSkills(ItemSkillType.NORMAL);
		if (skills != null && !skills.isEmpty())
		{
			skills.forEach(skillHolder -> SkillCaster.triggerCast(player, player, skillHolder.getSkill(), item, true));
		}

		// Send updated costume list
		player.sendPacket(new ExSendCostumeListFull());
	}
}