/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.clientpackets.costume;

import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.costumes.Costume;
import org.l2jmobius.gameserver.network.clientpackets.ClientPacket;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeLock;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExSendCostumeListFull;
import org.l2jmobius.gameserver.taskmanager.AttackStanceTaskManager;

/**
 * <AUTHOR>
 */
public class ExRequestCostumeLock extends ClientPacket
{
	private int		_id;
	private boolean	_lock;
	
	@Override
	protected void readImpl()
	{
		_id = readInt();
		_lock = readBoolean();
	}
	
	@Override
	protected void runImpl()
	{
		final Player player = getPlayer();
		if (player == null)
		{
			return;
		}

		java.util.logging.Logger.getLogger(ExRequestCostumeLock.class.getName()).info("Player " + player.getName() + " attempting to " + (_lock ? "lock" : "unlock") + " costume " + _id);

		if (AttackStanceTaskManager.getInstance().hasAttackStanceTask(player))
		{
			player.sendMessage("Cannot edit the lock tranformation setting during a battle.");
			return;
		}
		final Costume costume = player.getCostume(_id);
		if (costume != null)
		{
			// Update costume lock status
			costume.setLockedAndUpdate(_lock);

			// Send lock response packet
			player.sendPacket(new ExCostumeLock(_id, _lock, true));

			// Send updated costume list with delay to ensure client processes lock first
			org.l2jmobius.commons.threads.ThreadPool.schedule(() -> {
				player.sendPacket(new ExSendCostumeListFull());

				// Also refresh shortcut list to show lock status
				org.l2jmobius.commons.threads.ThreadPool.schedule(() -> {
					try
					{
						player.sendPacket(new org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeShortcutList(player.getObjectId()));
						java.util.logging.Logger.getLogger(ExRequestCostumeLock.class.getName()).info("Sent shortcut refresh after costume lock change for costume " + _id);
					}
					catch (Exception e)
					{
						java.util.logging.Logger.getLogger(ExRequestCostumeLock.class.getName()).warning("Failed to refresh shortcuts after lock change: " + e.getMessage());
					}
				}, 100); // 100ms delay for shortcut refresh
			}, 50); // 50ms delay for costume list

			java.util.logging.Logger.getLogger(ExRequestCostumeLock.class.getName()).info("Player " + player.getName() + " changed lock status of costume " + _id + " to " + _lock);
		}
	}
}