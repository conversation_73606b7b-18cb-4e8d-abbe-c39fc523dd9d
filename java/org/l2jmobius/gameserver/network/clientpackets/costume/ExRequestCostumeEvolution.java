/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.clientpackets.costume;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.costumes.CostumeEvolutionManager;
import org.l2jmobius.gameserver.model.costumes.CostumeEvolutionManager.EvolutionMaterial;
import org.l2jmobius.gameserver.model.costumes.CostumeEvolutionManager.EvolutionResult;
import org.l2jmobius.gameserver.network.clientpackets.ClientPacket;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeEvolution;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExSendCostumeList;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExSendCostumeListFull;

/**
 * <AUTHOR>
 */
public class ExRequestCostumeEvolution extends ClientPacket
{
	private static final Logger LOGGER = Logger.getLogger(ExRequestCostumeEvolution.class.getName());

	private int _costumeId;
	private List<EvolutionMaterial> _materials;
	
	@Override
	protected void readImpl()
	{
		readInt(); // amount costumes to evolve for now always 1
		_costumeId = readInt();
		readLong(); // amount of the costume for now always 1
		final int costumesUsed = readInt();

		_materials = new ArrayList<>(costumesUsed);
		for (int i = 0; i < costumesUsed; i++)
		{
			int materialId = readInt();
			long materialAmount = readLong();
			_materials.add(new EvolutionMaterial(materialId, materialAmount));
		}
	}
	
	@Override
	protected void runImpl()
	{
		final Player player = getPlayer();
		if (player == null)
		{
			return;
		}

		LOGGER.info("Player " + player.getName() + " attempting to evolve costume " + _costumeId);

		// Perform evolution using the new manager
		EvolutionResult result = CostumeEvolutionManager.evolve(player, _costumeId, _materials);

		if (result.isSuccess())
		{
			// Send success response
			player.sendPacket(ExCostumeEvolution.success(result.getModifiedCostumes(), result.getResultCostume()));
			player.sendPacket(new ExSendCostumeList(result.getModifiedCostumes()));
			player.sendPacket(new ExSendCostumeListFull()); // Refresh full list

			player.sendMessage("Costume evolution successful!");
			LOGGER.info("Player " + player.getName() + " successfully evolved costume " + _costumeId);
		}
		else
		{
			// Send failure response
			player.sendPacket(ExCostumeEvolution.failed());
			player.sendMessage(result.getErrorMessage());

			LOGGER.warning("Evolution failed for player " + player.getName() + ": " + result.getErrorMessage());
		}
	}
	

}