/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.clientpackets.costume;

import java.util.logging.Logger;

import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.costumes.Costume;
import org.l2jmobius.gameserver.model.costumes.CostumeExtractionManager;
import org.l2jmobius.gameserver.model.costumes.CostumeExtractionManager.ExtractionResult;
import org.l2jmobius.gameserver.model.costumes.CostumeManager;
import org.l2jmobius.gameserver.model.costumes.CostumeRecord;
import org.l2jmobius.gameserver.network.clientpackets.ClientPacket;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeExtract;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExSendCostumeList;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExSendCostumeListFull;

/**
 * <AUTHOR>
 */
public class ExRequestCostumeExtract extends ClientPacket
{
	private static final Logger LOGGER = Logger.getLogger(ExRequestCostumeExtract.class.getName());

	private int _costumeId;
	private long _amount;
	
	@Override
	protected void readImpl()
	{
		readShort(); // data size
		_costumeId = readInt();
		_amount = readLong();
	}
	
	@Override
	protected void runImpl()
	{
		final Player player = getPlayer();
		if (player == null)
		{
			return;
		}

		LOGGER.info("Player " + player.getName() + " attempting to extract " + _amount + " of costume " + _costumeId);

		// Debug: Check if costume exists in player's collection
		Costume playerCostume = player.getCostume(_costumeId);
		LOGGER.info("Player costume: " + (playerCostume != null ? "ID=" + playerCostume.getId() + ", Amount=" + playerCostume.getAmount() : "null"));

		// Debug: Check if costume exists in system
		CostumeRecord costumeRecord = CostumeManager.getInstance().getCostume(_costumeId);
		LOGGER.info("System costume record: " + (costumeRecord != null ? "ID=" + costumeRecord.id() + ", ExtractItem=" + costumeRecord.extractItem() : "null"));

		// Perform extraction using the new manager
		ExtractionResult result = CostumeExtractionManager.extract(player, _costumeId, _amount);

		if (result.isSuccess())
		{
			// Send success response with proper sequencing and delays
			player.sendPacket(ExCostumeExtract.success(result.getModifiedCostume(), result.getExtractedItemId(), result.getExtractedAmount()));

			// Force refresh player inventory first
			player.sendItemList();

			// Schedule delayed updates to ensure client processes them properly
			org.l2jmobius.commons.threads.ThreadPool.schedule(() -> {
				// First update the specific costume
				player.sendPacket(new ExSendCostumeList(result.getModifiedCostume()));
				LOGGER.info("Sent costume list update for costume " + _costumeId);

				// Then schedule full list refresh
				org.l2jmobius.commons.threads.ThreadPool.schedule(() -> {
					player.sendPacket(new ExSendCostumeListFull());
					LOGGER.info("Sent full costume list refresh");

					// Finally update shortcuts with multiple methods
					org.l2jmobius.commons.threads.ThreadPool.schedule(() -> {
						try
						{
							// Method 1: Send shortcut list
							player.sendPacket(new org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeShortcutList(player.getObjectId()));
							LOGGER.info("Sent shortcut list refresh after extraction");

							// Method 2: Force another full costume list with shortcuts
							org.l2jmobius.commons.threads.ThreadPool.schedule(() -> {
								try
								{
									player.sendPacket(new ExSendCostumeListFull());
									LOGGER.info("Sent costume list with shortcuts refresh");
								}
								catch (Exception ex)
								{
									LOGGER.warning("Failed to send costume list with shortcuts: " + ex.getMessage());
								}
							}, 100);

							// Force another inventory update to ensure everything is synced
							player.sendItemList();
							LOGGER.info("Sent final inventory update");

							// Schedule a final safety refresh after 1 second
							org.l2jmobius.commons.threads.ThreadPool.schedule(() -> {
								try
								{
									// Final safety refresh with both packets
									player.sendPacket(new ExSendCostumeListFull());
									player.sendPacket(new org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeShortcutList(player.getObjectId()));
									LOGGER.info("Sent final safety refresh for both costume list and shortcuts");
								}
								catch (Exception ex)
								{
									LOGGER.warning("Failed to send final safety refresh: " + ex.getMessage());
								}
							}, 1000); // 1 second safety delay
						}
						catch (Exception e)
						{
							LOGGER.warning("Failed to refresh shortcuts after extraction: " + e.getMessage());
						}
					}, 200); // 200ms delay for shortcuts
				}, 100); // 100ms delay for full list
			}, 50); // 50ms delay for costume list

			player.sendMessage("Costume extraction successful! Please wait a moment for the interface to update.");
			LOGGER.info("Player " + player.getName() + " successfully extracted " + _amount + " of costume " + _costumeId);
		}
		else
		{
			// Send failure response
			player.sendPacket(ExCostumeExtract.failed(_costumeId));
			player.sendMessage(result.getErrorMessage());

			LOGGER.warning("Extraction failed for player " + player.getName() + ": " + result.getErrorMessage());
		}
	}

}