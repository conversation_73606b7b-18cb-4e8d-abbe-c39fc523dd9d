# ---------------------------------------------------------------------------
# Game Server Settings
# ---------------------------------------------------------------------------
# This is the server configuration file. Here you can set up the connection information for your server.
# This was written with the assumption that you are behind a router.
# Dumbed Down Definitions...
# LAN (LOCAL area network) - typically consists of computers connected to the same router as you.
# WAN (WIDE area network) - typically consists of computers OUTSIDE of your router (ie. the internet).
# x.x.x.x - Format of an IP address. Do not include the x'es into settings. Must be real numbers.

# ---------------------------------------------------------------------------
# Networking
# ---------------------------------------------------------------------------

# Where's the Login server this gameserver should connect to
# WARNING: <u><b><font color="red">Please don't change default IPs here if you don't know what are you doing!</font></b></u>
# WARNING: <u><b><font color="red">External/Internal IPs are now inside "ipconfig.xml" file.</font></b></u>
# Default: 127.0.0.1
LoginHost = 127.0.0.1

# TCP port the login server listen to for gameserver connection requests
# Default: 9014
LoginPort = 9014

# Bind address for gameserver. You should not need to change it in most cases.
# WARNING: <u><b><font color="red">Please don't change default IPs here if you don't know what are you doing!</font></b></u>
# WARNING: <u><b><font color="red">External/Internal IPs are now inside "ipconfig.xml" file.</font></b></u>
# Default: * (0.0.0.0)
GameserverHostname = 0.0.0.0

# Default: 7777
GameserverPort = 7777

# Packet encryption.
# By default packets sent or received are encrypted using the Blowfish algorithm.
# Disabling this reduces the resources needed to process any packets transfered,
# also broadcasted packets do not need to be re-encrypted for each client sent.
# Retail: True
PacketEncryption = False


# ---------------------------------------------------------------------------
# Database
# ---------------------------------------------------------------------------

# Specify the JDBC driver class for your database.
# Default: org.mariadb.jdbc.Driver
Driver = org.mariadb.jdbc.Driver

# Database URL
# Default: ****************************************************************************************************************************************************************************,interactive_timeout=600&autoReconnect=true
URL = **********************************************************************************************************************************************************************************,interactive_timeout=600&autoReconnect=true

# Database user info. Default is "root" but it's not recommended.
Login = root

# Database user password, leave empty for no password.
Password = 123456

# Maximum number of database connections to maintain in the pool.
# Default: 100
MaximumDatabaseConnections = 100

# Determine whether database connections should be tested for availability.
# Default: False
TestDatabaseConnections = False


# ---------------------------------------------------------------------------
# Automatic Database Backup Settings
# ---------------------------------------------------------------------------
# Generate database backups when server restarts or shuts down. 
BackupDatabase = False

# Path to MySQL bin folder. Only necessary on Windows.
MySqlBinLocation = C:/xampp/mysql/bin/

# Path where MySQL backups are stored.
BackupPath = ../backup/

# Maximum number of days that backups will be kept.
# Old files in backup folder will be deleted.
# Set to 0 to disable.
BackupDays = 30


# ---------------------------------------------------------------------------
# Misc Server Settings
# ---------------------------------------------------------------------------

# This is the server ID that the Game Server will request.
# Example: 1 = Bartz
# Default: 1
RequestServerID = 1

# True = The Login Server will give an other ID to the server if the requested ID is already reserved.
# Default: True
AcceptAlternateID = True

# Datapack root directory.
# Defaults to current directory from which the server is started unless the below line is uncommented.
# WARNING: <u><b><font color="red">If the specified path is invalid, it will lead to multiple errors!</font></b></u>
# Default: .
DatapackRoot = .

# Scripts root directory.
ScriptRoot = ./data/scripts

# Define how many players are allowed to play simultaneously on your server.
# Default: 2000
MaximumOnlineUsers = 2000

# Numbers of protocol revisions that server allows to connect.
# Delimiter is ;
# WARNING: <u><b><font color="red">Changing the protocol revision may result in incompatible communication and many errors in game!</font></b></u>
# The Kamael: 228
AllowedProtocolRevisions = 228

# Displays server type next to the server name on character selection.
# Notes:
#	Accepted Values: Normal, Relax, Test, Broad, Restricted, Event, Free, World, New, Classic
# Default: Free
ServerListType = Classic

# Displays server minimum age to the server name on character selection.
# Notes:
#	Accepted values: 0, 15, 18
# Default: 0
ServerListAge = 0

# Setting for serverList
# Displays [] in front of server name on character selection
# Default: False
ServerListBrackets = False


# ---------------------------------------------------------------------------
# Thread Configuration
# ---------------------------------------------------------------------------

# Defines the number of threads in the scheduled thread pool.
# If set to -1, this will be determined by available processors multiplied by 4.
# You can specify a positive integer to manually set the pool size.
# Additionally, a high priority pool is created, sized at one quarter of the scheduled pool.
# Note that higher values can improve task handling under heavy load but may increase CPU and memory usage.
ScheduledThreadPoolSize = -1

# Defines the number of threads in the instant thread pool.
# If set to -1, this will be determined by available processors multiplied by 2.
# You can specify a positive integer to manually set the pool size.
# Note that higher values can improve task handling under heavy load but may increase CPU and memory usage.
InstantThreadPoolSize = -1

# Use threads to decrease startup time.
# Default: False
ThreadsForLoading = False

# It is not creating separate threads. Uses Threadpool instead.
# Splits the player / npc pool into several lists, to process with multiple threads.
# DO NOT CHANGE THIS WHILE SERVER IS RUNNING!
AutoPlayThreadCount = 6
AutoUseThreadCount = 2
AttackableAIThreadCount = 2
RespawnManagerThreadCount = 2


# ---------------------------------------------------------------------------
# Dead Lock Detector (separate thread for detecting deadlocks)
# ---------------------------------------------------------------------------
# For improved crash logs and automatic restart in deadlock case if enabled.
# Check interval is in seconds.
# Default: True
DeadLockDetector = True

# Default: 20
DeadLockCheckInterval = 20

# Default: False
RestartOnDeadlock = False


# ---------------------------------------------------------------------------
# Player HWID settings
# ---------------------------------------------------------------------------

# Check if hardware information is sent upon login.
# WARNING: To receive hardware information from client, l2.ini NetSendHardWare must be set to true.
# Default: False
EnableHardwareInfo = True

# Players without hardware information are kicked from the game.
# Automatically set to True when MaxPlayersPerHWID > 0.
# Default: False
KickMissingHWID = False

# Maximum number of players per HWID allowed to enter game.
# Default: 0 (unlimited)
MaxPlayersPerHWID = 0


# ---------------------------------------------------------------------------
# Misc Player Settings
# ---------------------------------------------------------------------------

# Character name template.
# Examples:
# CnameTemplate = [A-Z][a-z]{3,3}[A-Za-z0-9]*
# The above setting will allow names with first capital letter, next three small letters,
# and any letter (case insensitive) or number, like OmfgWTF1
# CnameTemplate = [A-Z][a-z]*
# The above setting will allow names only of letters with first one capital, like Omfgwtf
# Default .* (allows any symbol)
CnameTemplate = .*

# This setting restricts names players can give to their pets.
# See CnameTemplate for details
PetNameTemplate = .*

# This setting restricts clan/subpledge names players can set.
# See CnameTemplate for details
ClanNameTemplate = .*

# Maximum number of characters per account.
# Default: 7 (client limit)
CharMaxNumber = 7


# ---------------------------------------------------------------------------
# Precautionary Server Restart
# ---------------------------------------------------------------------------

# Enable server restart when CPU or memory usage is too high.
# Default: False
PrecautionaryRestartEnabled = False

# Enable monitoring system CPU usage.
# Default: True
PrecautionaryRestartCpu = True

# Enable monitoring process memory usage.
# Default: False
PrecautionaryRestartMemory = False

# Check if sieges are in progress
# or players are in olympiad, events, instances
# or have targeted raidbosses.
# Default: True
PrecautionaryRestartChecks = True

# Percentage of used resources.
# Default: 95
PrecautionaryRestartPercentage = 95

# Delay in seconds between each check.
# Default: 60
PrecautionaryRestartDelay = 60


# ---------------------------------------------------------------------------
# Scheduled Server Restart
# ---------------------------------------------------------------------------

# Enable scheduled server restart.
# Default: False
ServerRestartScheduleEnabled = False

# Send a message when player enters the game.
# Default: False
ServerRestartScheduleMessage = False

# Restart time countdown (in seconds).
# Default: 600 (10 minutes)
ServerRestartScheduleCountdown = 600

# Scheduled restart schedule.
# You can put more than one value separated by commas (,).
# Example: 12:00, 00:00
ServerRestartSchedule = 05:00

# Specify days that the restart will occur. Values separated by commas (,).
# Example: 1,2,3,4,5,6,7 (SUNDAY,MONDAY,TUESDAY,WEDNESDAY,THURSDAY,FRIDAY,SATURDAY)
# Default: 4 (WEDNESDAY)
ServerRestartDays = 2,4,7
