<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="list">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="costume" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="extract">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="cost" maxOccurs="unbounded">
											<xs:complexType>
												<xs:attribute name="id" type="xs:int" use="required" />
												<xs:attribute name="count" type="xs:int" use="required" />
											</xs:complexType>
										</xs:element>
									</xs:sequence>
									<xs:attribute name="item" type="xs:int" use="required" />
								</xs:complexType>
							</xs:element>
						</xs:sequence>
						<xs:attribute name="id" type="xs:int" use="required" />
						<xs:attribute name="skill" type="xs:int" use="required" />
						<xs:attribute name="evolution-fee" type="xs:int" use="required" />
						<xs:attribute name="grade" type="xs:string" use="optional" />
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>