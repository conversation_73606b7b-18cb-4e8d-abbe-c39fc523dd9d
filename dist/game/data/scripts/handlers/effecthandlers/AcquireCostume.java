package handlers.effecthandlers;

import org.l2jmobius.gameserver.model.StatSet;
import org.l2jmobius.gameserver.model.actor.Creature;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.costumes.Costume;
import org.l2jmobius.gameserver.model.effects.AbstractEffect;
import org.l2jmobius.gameserver.model.item.instance.Item;
import org.l2jmobius.gameserver.model.skill.Skill;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeUseItem;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExSendCostumeList;

public class AcquireCostume extends AbstractEffect
{
	private final int id;
	
	public AcquireCostume(StatSet params)
	{
		id = params.getInt("id", 0);
	}
	
	@Override
	public boolean isInstant()
	{
		return true;
	}
	
	@Override
	public void instant(Creature effector, Creature effected, Skill skill, Item item)
	{
		final Player player = effected.getActingPlayer();
		if (player == null)
		{
			return;
		}
		Costume costume = player.addCostume(id);
		if (costume == null)
		{
			return;
		}
		player.sendPacket(new ExCostumeUseItem(id, true));
		player.sendPacket(new ExSendCostumeList(costume));
		player.destroyItem("Consume", item, 1, null, true);
	}
}
