package ai.areas.forestMirrors;

import ai.AbstractNpcAI;
import org.l2jmobius.gameserver.model.actor.Npc;
import org.l2jmobius.gameserver.model.actor.Playable;
import org.l2jmobius.gameserver.model.actor.Player;

public class <PERSON>t extends AbstractNpcAI
{
    private static final int HARIT = 21658;

    private static final int NPCS[] =
            {
                    20635, // Carinkain
                    20632, // Taik Orc Warrior
                    20633, // Taik Orc Shaman
                    20630, // Taik Orc
                    20631, // Taik Orc Archer
                    20636, // Forest of Mirrors Ghost
                    20639, // Mirror
                    20640, // <PERSON><PERSON>man
                    20641, // <PERSON><PERSON>man Soldier
                    20642, // Harit Lizardman Archer
                    20643, // Harit Liza<PERSON>man Warrior
                    20644, // Harit Lizardman Shaman
                    20645, // Harit Lizardman Matriarch
            };

    private Harit() {
        addKillId(NPCS);
    }

    public static void main(String[] args) {
        new Harit();
    }
    @Override
    public String onKill(Npc npc, Player killer, boolean isSummon) {
        if (getRandom(100) < 30) {
            final Npc spawnGuard = addSpawn(HARIT, npc, false, 300000);
            addAttackPlayerDesire(spawnGuard, killer);
        }
        return super.onKill(npc, killer, isSummon);
    }
}
