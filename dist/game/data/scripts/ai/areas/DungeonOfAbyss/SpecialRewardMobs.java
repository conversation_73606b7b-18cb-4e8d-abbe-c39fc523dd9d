/*
 * This file is part of the L2J Mobius project.
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package ai.areas.DungeonOfAbyss;

import org.l2jmobius.gameserver.model.actor.Npc;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.holders.ItemHolder;

import ai.AbstractNpcAI;

/**
 * Special Reward Mobs AI for Dungeon of Abyss
 * <AUTHOR>
 */
public class SpecialRewardMobs extends AbstractNpcAI
{
	// NPCs
	private static final int CONDEMNED_OF_INFINITY = 21643;
	private static final int ABYSS_BRUTE = 21649;
	
	// Items for NPC 21643 (Condemned of Infinity)
	private static final ItemHolder GEM_RANK_D = new ItemHolder(2130, 1); // Gem: Rank D
	private static final ItemHolder ACCESSORY_CHEST_C = new ItemHolder(49669, 1); // Accessory Chest - Rank C
	private static final ItemHolder GEM_RANK_C = new ItemHolder(2131, 1); // Gem: Rank C
	
	// Items for NPC 21649 (Abyss Brute)
	private static final ItemHolder ARMOR_CHEST_C = new ItemHolder(49668, 1); // Armor Chest - Rank C
	private static final ItemHolder GEM_RANK_B = new ItemHolder(2132, 1); // Gem: Rank B
	
	private SpecialRewardMobs()
	{
		addKillId(CONDEMNED_OF_INFINITY, ABYSS_BRUTE);
	}
	
	@Override
	public String onKill(Npc npc, Player killer, boolean isSummon)
	{
		switch (npc.getId())
		{
			case CONDEMNED_OF_INFINITY:
			{
				// Give 3 items: Gem D, Accessory Chest C, Gem C
				giveItems(killer, GEM_RANK_D);
				giveItems(killer, ACCESSORY_CHEST_C);
				giveItems(killer, GEM_RANK_C);
				killer.sendMessage("You obtained special rewards from Condemned of Infinity!");
				break;
			}
			case ABYSS_BRUTE:
			{
				// Give 5 items: Gem D, Accessory Chest C, Gem C, Armor Chest C, Gem B
				giveItems(killer, GEM_RANK_D);
				giveItems(killer, ACCESSORY_CHEST_C);
				giveItems(killer, GEM_RANK_C);
				giveItems(killer, ARMOR_CHEST_C);
				giveItems(killer, GEM_RANK_B);
				killer.sendMessage("You obtained special rewards from Abyss Brute!");
				break;
			}
		}
		
		return super.onKill(npc, killer, isSummon);
	}
	
	public static void main(String[] args)
	{
		new SpecialRewardMobs();
	}
}
